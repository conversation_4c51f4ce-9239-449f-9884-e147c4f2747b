Stack trace:
Frame         Function      Args
0007FFFFA170  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9070) msys-2.0.dll+0x1FEBA
0007FFFFA170  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA448) msys-2.0.dll+0x67F9
0007FFFFA170  000210046832 (000210285FF9, 0007FFFFA028, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA170  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA170  0002100690B4 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA450  00021006A49D (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD4F360000 ntdll.dll
7FFD4D5E0000 KERNEL32.DLL
7FFD4C8B0000 KERNELBASE.dll
7FFD4EE50000 USER32.dll
7FFD4D0D0000 win32u.dll
7FFD4D730000 GDI32.dll
7FFD4C630000 gdi32full.dll
7FFD4C800000 msvcp_win.dll
7FFD4CCA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD4D1B0000 advapi32.dll
7FFD4D950000 msvcrt.dll
7FFD4F030000 sechost.dll
7FFD4D310000 RPCRT4.dll
7FFD4BBD0000 CRYPTBASE.DLL
7FFD4CDF0000 bcryptPrimitives.dll
7FFD4D110000 IMM32.DLL
