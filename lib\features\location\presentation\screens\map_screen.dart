import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:rozana/core/config/environment_config.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:uuid/uuid.dart';
// import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_button.dart';
import '../../services/adress_services.dart';

class MapScreen extends StatefulWidget {
  final AddressModel? address;
  final Function(double latitude, double longitude)? onLocationSelected;
  final bool fromCart;
  const MapScreen({
    super.key,
    this.address,
    this.fromCart = false,
    this.onLocationSelected,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final AddressService _addressService = AddressService();

  GoogleMapController? _mapController;
  double? _currentLatitude;
  double? _currentLongitude;
  bool _isLoading = false;
  bool _hasLocationError = false;
  String _errorMessage = '';
  String _currentAddress = 'Loading address...';
  final token = Uuid().v4();
  List<dynamic>? locations;

  // Add search controller
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  void _initializeLocation() async {
    try {
      setState(() {
        _hasLocationError = false;
        _errorMessage = '';
      });

      if (widget.address != null &&
          widget.address!.latitude != null &&
          widget.address!.longitude != null) {
        await _setLocationAndFetchAddress(
          (widget.address!.latitude as double),
          (widget.address!.longitude as double),
        );
      } else {
        final position = await _addressService.getCurrentPosition();
        if (position != null) {
          await _setLocationAndFetchAddress(
              position.latitude, position.longitude);
        } else {
          // Use Delhi coordinates as fallback
          await _setLocationAndFetchAddress(28.6139, 77.2090);
        }
      }
    } catch (e) {
      setState(() {
        _hasLocationError = true;
        _errorMessage = 'Failed to load location. Please try again.';
      });
    }
  }

  Future<void> _setLocationAndFetchAddress(
      double latitude, double longitude) async {
    setState(() {
      _currentLatitude = latitude;
      _currentLongitude = longitude;
    });
    await _getAddressFromCoordinates(latitude, longitude);
  }

  Future<void> _getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      final placemarks = await _addressService.getAddressFromCoordinates(
        latitude,
        longitude,
      );

      if (placemarks != null && placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        setState(() {
          _currentAddress = _buildAddressString(placemark);
        });
      } else {
        setState(() {
          _currentAddress = 'Address not found';
        });
      }
    } catch (e) {
      setState(() {
        _currentAddress = 'Failed to get address';
      });
    }
  }

  String _buildAddressString(dynamic placemark) {
    List<String> addressParts = [];

    if (placemark.street != null && placemark.street!.isNotEmpty) {
      addressParts.add(placemark.street!);
    }
    if (placemark.locality != null && placemark.locality!.isNotEmpty) {
      addressParts.add(placemark.locality!);
    }
    if (placemark.administrativeArea != null &&
        placemark.administrativeArea!.isNotEmpty) {
      addressParts.add(placemark.administrativeArea!);
    }
    if (placemark.postalCode != null && placemark.postalCode!.isNotEmpty) {
      addressParts.add(placemark.postalCode!);
    }

    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Unknown location';
  }

  void _onCameraMove(CameraPosition position) {
    // Update coordinates as map moves
    _currentLatitude = position.target.latitude;
    _currentLongitude = position.target.longitude;
  }

  void _onCameraIdle() {
    if (_currentLatitude != null && _currentLongitude != null) {
      _getAddressFromCoordinates(_currentLatitude!, _currentLongitude!);
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final position = await _addressService.getCurrentPosition();

      if (position != null) {
        await _setLocationAndFetchAddress(
            position.latitude, position.longitude);
        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: LatLng(position.latitude, position.longitude),
              zoom: 16,
            ),
          ),
        );
      } else {
        _showSnackBar('Failed to get current location');
      }
    } catch (e) {
      _showSnackBar('Failed to get current location');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _selectLocation() async {
    if (_currentLatitude == null || _currentLongitude == null) return;
    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(_currentLatitude!, _currentLongitude!);
    }
    // Get placemark for selected location
    final placemarks = await _addressService.getAddressFromCoordinates(
        _currentLatitude!, _currentLongitude!);
    AddressModel? addressModel;
    if (placemarks != null && placemarks.isNotEmpty) {
      final placemark = placemarks.first;
      addressModel = AddressModel(
        id: widget.address?.id, // preserve id if editing
        fullAddress: _buildAddressString(placemark),
        addressLine1: placemark.street ?? '',
        city: placemark.locality ?? '',
        state: placemark.administrativeArea ?? '',
        pincode: placemark.postalCode ?? '',
        latitude: _currentLatitude!,
        longitude: _currentLongitude!,
        addressType: widget.address?.addressType ?? 'home',
        isDefault: widget.address?.isDefault ?? false,
      );
    }

    // If address was created/saved and we're coming from cart, return the address
    if (!mounted) return;
    if (widget.fromCart) {
      final result = await context.push<AddressModel>(
        RouteNames.editAddress,
        extra: {'address': addressModel, 'fromCart': widget.fromCart},
      );
      context.pop(result);
    } else {
      context.pushReplacement(
        RouteNames.editAddress,
        extra: {'address': addressModel, 'fromCart': widget.fromCart},
      );
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  Future<void> _searchAndMoveToLocation(String query) async {
    if (query.trim().isEmpty) return;
    setState(() {
      _isSearching = true;
    });
    try {
      final locations = await _addressService.searchAddresses(query);
      if (locations != null && locations.isNotEmpty) {
        final location = locations.first;
        final placemarks =
            await _addressService.getPlacemarkFromLocation(location);
        if (placemarks != null && placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          setState(() {
            _currentLatitude = location.latitude;
            _currentLongitude = location.longitude;
            _currentAddress = _buildAddressString(placemark);
          });
          _mapController?.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(_currentLatitude!, _currentLongitude!),
                zoom: 16,
              ),
            ),
          );
        } else {
          _showSnackBar('Address not found for this location');
        }
      } else {
        _showSnackBar('Location not found');
      }
    } catch (e) {
      _showSnackBar('Failed to search location');
    } finally {
      setState(() {
        _isSearching = false;
      });
    }
  }

  Future<void> placeSuggestion(String input) async {
    String apiKey = EnvironmentConfig.googlePlacesApiKey;
    try {
      String baseUrl =
          EnvironmentConfig.googleMapsBaseUrl; // Use the base URL from config
      String request = '$baseUrl?input=$input&key=$apiKey&sessiontoken=$token';
      var response = await http.get(Uri.parse(request));

      if (response.statusCode == 200) {
        setState(() {
          locations = json.decode(response.body)['predictions'];
        });
      } else {
        throw Exception('Failed to load locations');
      }
    } catch (e) {
      // Log error in debug mode only
      if (mounted) {
        _showSnackBar('Failed to load place suggestions');
      }
    }
  }

  void _onChange(String value) {
    placeSuggestion(value);
  }

  Widget _buildErrorState(String message) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Location'),
        backgroundColor: AppColors.primaryAverage,
        foregroundColor: AppColors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 80,
                color: AppColors.error,
              ),
              const SizedBox(height: 16),
              const Text(
                'Location Error',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 24),
              CustomButton(
                text: 'Try Again',
                onPressed: _initializeLocation,
                backgroundColor: AppColors.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Show error state if there's a location error
    if (_hasLocationError) {
      return _buildErrorState(_errorMessage);
    }

    // Show loading state if location is not yet available
    if (_currentLatitude == null || _currentLongitude == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Select Location'),
          backgroundColor: AppColors.primaryAverage,
          foregroundColor: AppColors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                'Loading location...',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      );
    }
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.textPrimary,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowGrey,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          ),
        ),
        title: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowGrey,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Text(
            'Select Location',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        centerTitle: true,
      ),
      body: Stack(
        children: [
          // Google Map with modern styling
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: LatLng(_currentLatitude!, _currentLongitude!),
              zoom: 16,
            ),
            onMapCreated: (controller) {
              _mapController = controller;
            },
            onCameraMove: _onCameraMove,
            onCameraIdle: _onCameraIdle,
            myLocationEnabled: false, // We'll use custom location button
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false, // We'll use custom zoom controls
            mapToolbarEnabled: false,
            compassEnabled: false,
            style: '''[
              {
                "featureType": "poi",
                "elementType": "labels",
                "stylers": [{"visibility": "off"}]
              }
            ]''', // Hide POI labels for cleaner look
          ),

          // Modern Search Bar
          Positioned(
            top: MediaQuery.of(context).padding.top + 80,
            left: 16,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowGrey,
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: TextField(
                onChanged: (value) => _onChange(value),
                controller: _searchController,
                textInputAction: TextInputAction.search,
                onSubmitted: (value) {
                  _searchAndMoveToLocation(value);
                  FocusScope.of(context).unfocus();
                  setState(() {
                    locations = [];
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Search for restaurants, shops, areas...',
                  hintStyle: const TextStyle(
                    color: AppColors.textHint,
                    fontSize: 14,
                  ),
                  prefixIcon: const Icon(
                    Icons.search,
                    color: AppColors.textSecondary,
                    size: 20,
                  ),
                  suffixIcon: _isSearching
                      ? const Padding(
                          padding: EdgeInsets.all(12),
                          child: SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.primary),
                            ),
                          ),
                        )
                      : (_searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(
                                Icons.clear,
                                color: AppColors.textSecondary,
                                size: 20,
                              ),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  locations = [];
                                });
                              },
                            )
                          : null),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
              ),
            ),
          ),

          // Modern Search Results
          if (locations != null && locations!.isNotEmpty)
            Positioned(
              top: MediaQuery.of(context).padding.top + 140,
              left: 16,
              right: 16,
              child: Container(
                constraints: const BoxConstraints(maxHeight: 250),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowGrey,
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ListView.separated(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  shrinkWrap: true,
                  itemCount: locations!.length,
                  separatorBuilder: (context, index) => const Divider(
                    height: 1,
                    color: AppColors.background,
                  ),
                  itemBuilder: (context, index) {
                    final location = locations![index];
                    return ListTile(
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.location_on,
                          color: AppColors.primary,
                          size: 16,
                        ),
                      ),
                      title: Text(
                        location['description'],
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      onTap: () {
                        _searchAndMoveToLocation(location['description']);
                        setState(() {
                          locations = [];
                          _searchController.text = location['description'];
                          FocusScope.of(context).unfocus();
                        });
                      },
                    );
                  },
                ),
              ),
            ),

          // Modern Center Marker with Animation
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.location_on,
                    color: AppColors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: 4,
                  height: 20,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ],
            ),
          ),

          // Floating Action Buttons
          Positioned(
            right: 16,
            bottom: 200,
            child: Column(
              children: [
                FloatingActionButton(
                  heroTag: "location",
                  mini: true,
                  backgroundColor: AppColors.surface,
                  foregroundColor: AppColors.textPrimary,
                  elevation: 4,
                  onPressed: _getCurrentLocation,
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primary),
                          ),
                        )
                      : const Icon(Icons.my_location, size: 20),
                ),
                const SizedBox(height: 8),
                FloatingActionButton(
                  heroTag: "zoom_in",
                  mini: true,
                  backgroundColor: AppColors.surface,
                  foregroundColor: AppColors.textPrimary,
                  elevation: 4,
                  onPressed: () {
                    _mapController?.animateCamera(CameraUpdate.zoomIn());
                  },
                  child: const Icon(Icons.add, size: 20),
                ),
                const SizedBox(height: 8),
                FloatingActionButton(
                  heroTag: "zoom_out",
                  mini: true,
                  backgroundColor: AppColors.surface,
                  foregroundColor: AppColors.textPrimary,
                  elevation: 4,
                  onPressed: () {
                    _mapController?.animateCamera(CameraUpdate.zoomOut());
                  },
                  child: const Icon(Icons.remove, size: 20),
                ),
              ],
            ),
          ),

          // Modern Bottom Sheet
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowGrey,
                    blurRadius: 20,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppColors.textHint,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Location icon and title
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.location_on,
                          color: AppColors.primary,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'Delivery Location',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Address
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.background,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.place,
                          color: AppColors.textSecondary,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _currentAddress,
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.textPrimary,
                              height: 1.4,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Action button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _selectLocation,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.check_circle, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'Confirm Location',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
